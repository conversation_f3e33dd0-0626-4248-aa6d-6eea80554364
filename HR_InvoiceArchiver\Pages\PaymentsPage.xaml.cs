using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Pages
{
    public partial class PaymentsPage : UserControl, INavigationAware
    {
        private readonly IPaymentService _paymentService;
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;
        private ObservableCollection<Payment> _allPayments;
        private ObservableCollection<Payment> _filteredPayments;

        public PaymentsPage(
            IPaymentService paymentService,
            IToastService toastService,
            INavigationService navigationService)
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: Constructor started");
                InitializeComponent();

                _paymentService = paymentService ?? throw new ArgumentNullException(nameof(paymentService));
                _toastService = toastService ?? throw new ArgumentNullException(nameof(toastService));
                _navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));

                _allPayments = new ObservableCollection<Payment>();
                _filteredPayments = new ObservableCollection<Payment>();

                PaymentsDataGrid.ItemsSource = _filteredPayments;

                System.Console.WriteLine("PaymentsPage: Constructor completed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: Constructor error: {ex.Message}");
                System.Console.WriteLine($"PaymentsPage: Stack trace: {ex.StackTrace}");
                throw;
            }
        }



        public async void OnNavigatedTo(object parameter)
        {
            System.Console.WriteLine("PaymentsPage: OnNavigatedTo called");
            try
            {
                // Handle navigation parameters
                if (parameter is string action && action == "add")
                {
                    System.Console.WriteLine("PaymentsPage: Add payment parameter detected");
                    AddPaymentButton_Click(this, new RoutedEventArgs());
                }
                else if (parameter is string multiAction && multiAction == "multi")
                {
                    System.Console.WriteLine("PaymentsPage: Multi payment parameter detected");
                    AddMultiPaymentButton_Click(this, new RoutedEventArgs());
                }
                else
                {
                    System.Console.WriteLine("PaymentsPage: Loading payments data");
                    await LoadPaymentsAsync();
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: OnNavigatedTo error: {ex.Message}");
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء التنقل: {ex.Message}");
            }
        }

        public void OnNavigatedFrom()
        {
            System.Console.WriteLine("PaymentsPage: OnNavigatedFrom called");
        }

        private async Task LoadPaymentsAsync()
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: LoadPaymentsAsync started");

                // Show loading
                LoadingPanel.Visibility = Visibility.Visible;
                EmptyStatePanel.Visibility = Visibility.Collapsed;

                // Load payments
                var payments = await _paymentService.GetAllPaymentsAsync();

                _allPayments.Clear();
                _filteredPayments.Clear();

                foreach (var payment in payments)
                {
                    _allPayments.Add(payment);
                    _filteredPayments.Add(payment);
                }

                // Update statistics
                await UpdateStatisticsAsync();

                // Hide loading and show content
                LoadingPanel.Visibility = Visibility.Collapsed;

                if (_filteredPayments.Count == 0)
                {
                    EmptyStatePanel.Visibility = Visibility.Visible;
                }
                else
                {
                    EmptyStatePanel.Visibility = Visibility.Collapsed;
                }

                // Update last update time
                LastUpdateText.Text = DateTime.Now.ToString("HH:mm:ss");

                System.Console.WriteLine("PaymentsPage: LoadPaymentsAsync completed successfully");
                _toastService?.ShowSuccess("تم التحديث", "تم تحميل المدفوعات بنجاح");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: LoadPaymentsAsync error: {ex.Message}");
                LoadingPanel.Visibility = Visibility.Collapsed;
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء تحميل المدفوعات: {ex.Message}");
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var statistics = await _paymentService.GetPaymentStatisticsAsync();

                // Update statistics cards
                TotalPaymentsText.Text = statistics.TotalPayments.ToString("N0");
                TotalAmountText.Text = $"{statistics.TotalAmount:N0}";

                // Update cash vs card statistics
                var cashCount = _allPayments.Count(p => p.Method == PaymentMethod.Cash);
                var cardCount = _allPayments.Count(p => p.Method == PaymentMethod.CreditCard);

                CashPaymentsText.Text = cashCount.ToString("N0");
                CardPaymentsText.Text = cardCount.ToString("N0");

                // Update payment count
                PaymentCountText.Text = $"{_filteredPayments.Count} دفعة";

                System.Console.WriteLine("PaymentsPage: Statistics updated successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: UpdateStatisticsAsync error: {ex.Message}");
            }
        }

        private void FilterPayments()
        {
            try
            {
                var searchText = SearchTextBox.Text?.ToLower() ?? "";

                var filtered = _allPayments.Where(payment =>
                {
                    // Search filter
                    bool matchesSearch = string.IsNullOrEmpty(searchText) ||
                                       payment.ReceiptNumber.ToLower().Contains(searchText) ||
                                       payment.SupplierName.ToLower().Contains(searchText) ||
                                       payment.Details.ToLower().Contains(searchText);

                    return matchesSearch;
                }).ToList();

                _filteredPayments.Clear();
                foreach (var payment in filtered)
                {
                    _filteredPayments.Add(payment);
                }

                // Update UI based on filtered results
                if (_filteredPayments.Count == 0 && _allPayments.Count > 0)
                {
                    EmptyStatePanel.Visibility = Visibility.Visible;
                }
                else if (_filteredPayments.Count > 0)
                {
                    EmptyStatePanel.Visibility = Visibility.Collapsed;
                }

                // Update count
                PaymentCountText.Text = $"{_filteredPayments.Count} دفعة";
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: FilterPayments error: {ex.Message}");
            }
        }

        // Event Handlers
        private void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addEditPaymentWindow = App.ServiceProvider.GetService(typeof(Windows.AddEditPaymentWindow)) as Windows.AddEditPaymentWindow;
                if (addEditPaymentWindow != null)
                {
                    addEditPaymentWindow.Owner = Window.GetWindow(this);
                    addEditPaymentWindow.ShowDialog();

                    // Refresh the payments list after closing
                    LoadPaymentsAsync();
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء فتح نافذة إضافة الدفعة: {ex.Message}");
            }
        }

        private void AddMultiPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowMultiPaymentForm();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء فتح نافذة الدفع المتعدد: {ex.Message}");
            }
        }

        private void ShowMultiPaymentForm()
        {
            // Clear existing content
            MultiPaymentContainer.Children.Clear();

            // Create new MultiPaymentFormControl
            var multiPaymentForm = new Controls.MultiPaymentFormControl();
            multiPaymentForm.FormClosed += MultiPaymentForm_FormClosed;

            // Add to container
            MultiPaymentContainer.Children.Add(multiPaymentForm);

            // Show overlay with animation
            MultiPaymentOverlay.Visibility = Visibility.Visible;

            // Fade in animation
            var fadeIn = new System.Windows.Media.Animation.DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromMilliseconds(300)
            };
            MultiPaymentOverlay.BeginAnimation(UIElement.OpacityProperty, fadeIn);
        }

        private void MultiPaymentForm_FormClosed(object sender, Controls.MultiPaymentEventArgs e)
        {
            // Hide overlay with animation
            var fadeOut = new System.Windows.Media.Animation.DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(300)
            };

            fadeOut.Completed += (s, args) =>
            {
                MultiPaymentOverlay.Visibility = Visibility.Collapsed;
                MultiPaymentContainer.Children.Clear();
            };

            MultiPaymentOverlay.BeginAnimation(UIElement.OpacityProperty, fadeOut);

            // Refresh payments list if payment was successful
            if (e.Success)
            {
                LoadPaymentsAsync();
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadPaymentsAsync();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterPayments();
        }

        private void ClearSearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            _toastService?.ShowInfo("تم المسح", "تم مسح البحث");
        }

        private void AdvancedSearchButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("البحث المتقدم", "سيتم إضافة البحث المتقدم قريباً");
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("تصدير", "سيتم إضافة ميزة التصدير قريباً");
        }

        // Payment Actions Event Handlers
        private void ViewReceiptAttachment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Payment payment)
                {
                    if (string.IsNullOrEmpty(payment.AttachmentPath))
                    {
                        _toastService?.ShowWarning("تنبيه", "لا يوجد مرفق لهذا الوصل");
                        return;
                    }

                    OpenPaymentAttachment(payment);
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في عرض مرفق الوصل: {ex.Message}");
            }
        }

        private void ViewPaymentDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Payment payment)
                {
                    ShowPaymentDetailsDialog(payment);
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في عرض تفاصيل الدفعة: {ex.Message}");
            }
        }

        private async void EditPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Payment payment)
                {
                    var addEditPaymentWindow = App.ServiceProvider.GetService(typeof(Windows.AddEditPaymentWindow)) as Windows.AddEditPaymentWindow;
                    if (addEditPaymentWindow != null)
                    {
                        // Set payment for editing - assuming constructor or property exists
                        addEditPaymentWindow.Owner = Window.GetWindow(this);
                        addEditPaymentWindow.ShowDialog();

                        // Refresh the payments list after closing
                        await LoadPaymentsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في فتح نافذة تعديل الدفعة: {ex.Message}");
            }
        }

        private async void DeletePayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Payment payment)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف الدفعة رقم {payment.ReceiptNumber}؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning,
                        MessageBoxResult.No);

                    if (result == MessageBoxResult.Yes)
                    {
                        await _paymentService.DeletePaymentAsync(payment.Id);
                        _toastService?.ShowSuccess("تم الحذف", $"تم حذف الدفعة رقم {payment.ReceiptNumber} بنجاح");
                        await LoadPaymentsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في حذف الدفعة: {ex.Message}");
            }
        }

        // Helper Methods
        private void OpenPaymentAttachment(Payment payment)
        {
            try
            {
                var fullPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Attachments", payment.AttachmentPath ?? "");

                if (File.Exists(fullPath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = fullPath,
                        UseShellExecute = true
                    });
                    _toastService?.ShowSuccess("تم فتح المرفق", $"تم فتح مرفق الوصل رقم {payment.ReceiptNumber}");
                }
                else
                {
                    _toastService?.ShowError("ملف غير موجود", "مرفق الوصل غير موجود في المسار المحدد");
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في فتح الملف", $"فشل في فتح مرفق الوصل: {ex.Message}");
            }
        }

        private void ShowPaymentDetailsDialog(Payment payment)
        {
            try
            {
                var dialog = new Window
                {
                    Title = $"تفاصيل الدفعة - {payment.ReceiptNumber}",
                    Width = 500,
                    Height = 600,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this),
                    ResizeMode = ResizeMode.NoResize,
                    WindowStyle = WindowStyle.ToolWindow
                };

                var scrollViewer = new ScrollViewer
                {
                    VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                    Padding = new Thickness(20)
                };

                var stackPanel = new StackPanel
                {
                    Orientation = Orientation.Vertical
                };

                // Payment Details
                var details = new[]
                {
                    new { Label = "رقم الوصل:", Value = payment.ReceiptNumber },
                    new { Label = "رقم الفاتورة:", Value = payment.InvoiceNumber },
                    new { Label = "اسم المورد:", Value = payment.SupplierName },
                    new { Label = "المبلغ:", Value = $"{payment.Amount:N0} د.ع" },
                    new { Label = "مبلغ الخصم:", Value = $"{payment.DiscountAmount:N0} د.ع" },
                    new { Label = "قيمة الاسترجاع:", Value = $"{payment.RefundValue:N0} د.ع" },
                    new { Label = "تاريخ الدفع:", Value = payment.PaymentDate.ToString("dd/MM/yyyy") },
                    new { Label = "طريقة الدفع:", Value = payment.PaymentMethodText },
                    new { Label = "حالة التسديد:", Value = payment.StatusText },
                    new { Label = "التفاصيل:", Value = payment.Details },
                    new { Label = "تاريخ الإنشاء:", Value = payment.CreatedDate.ToString("dd/MM/yyyy HH:mm") }
                };

                foreach (var detail in details)
                {
                    var panel = new StackPanel
                    {
                        Orientation = Orientation.Horizontal,
                        Margin = new Thickness(0, 5, 0, 5)
                    };

                    var label = new TextBlock
                    {
                        Text = detail.Label,
                        FontWeight = FontWeights.Bold,
                        Width = 120,
                        Margin = new Thickness(0, 0, 10, 0)
                    };

                    var value = new TextBlock
                    {
                        Text = detail.Value,
                        TextWrapping = TextWrapping.Wrap
                    };

                    panel.Children.Add(label);
                    panel.Children.Add(value);
                    stackPanel.Children.Add(panel);
                }

                // Attachment section
                if (!string.IsNullOrEmpty(payment.AttachmentPath))
                {
                    var separator = new Separator { Margin = new Thickness(0, 10, 0, 10) };
                    stackPanel.Children.Add(separator);

                    var attachmentButton = new Button
                    {
                        Content = "عرض المرفق",
                        Margin = new Thickness(0, 10, 0, 10),
                        Padding = new Thickness(15, 8, 15, 8),
                        HorizontalAlignment = HorizontalAlignment.Center
                    };

                    attachmentButton.Click += (s, e) =>
                    {
                        OpenPaymentAttachment(payment);
                        dialog.Close();
                    };

                    stackPanel.Children.Add(attachmentButton);
                }

                scrollViewer.Content = stackPanel;
                dialog.Content = scrollViewer;
                dialog.ShowDialog();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في عرض تفاصيل الدفعة: {ex.Message}");
            }
        }
    }
}
