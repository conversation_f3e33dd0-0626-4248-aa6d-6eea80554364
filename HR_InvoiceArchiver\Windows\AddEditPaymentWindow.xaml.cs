using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Win32;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Utils;

namespace HR_InvoiceArchiver.Windows
{
    public partial class AddEditPaymentWindow : Window, INotifyPropertyChanged
    {
        private readonly IPaymentService _paymentService;
        private readonly IInvoiceService _invoiceService;
        private readonly IToastService _toastService;
        
        public event PropertyChangedEventHandler? PropertyChanged;
        
        private Payment? _currentPayment;
        private bool _isEditMode;
        private string? _selectedAttachmentPath; // مسار الملف المحدد حديثاً
        
        public string WindowTitle => _isEditMode ? "تعديل مدفوعة" : "إضافة مدفوعة جديدة";
        
        public AddEditPaymentWindow(Payment? payment = null)
        {
            // Initialize services from DI container
            _paymentService = App.ServiceProvider.GetRequiredService<IPaymentService>();
            _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();
            
            _currentPayment = payment;
            _isEditMode = payment != null;
            
            InitializeComponent();
            DataContext = this;
            
            Loaded += AddEditPaymentWindow_Loaded;
        }
        
        private async void AddEditPaymentWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadInvoicesAsync();
            
            if (_isEditMode && _currentPayment != null)
            {
                LoadPaymentData();
            }
            else
            {
                // Set default values for new payment
                PaymentDatePicker.SelectedDate = DateTime.Today;
                PaymentMethodComboBox.SelectedIndex = 0; // Default to Cash
                PaymentStatusComboBox.SelectedIndex = 0; // Default to FullPayment
            }
        }
        
        private async Task LoadInvoicesAsync()
        {
            try
            {
                var allInvoices = await _invoiceService.GetAllInvoicesAsync();
                
                // For new payments, only show invoices that can accept payments
                // For editing, include the current payment's invoice even if it's fully paid
                var eligibleInvoices = _isEditMode && _currentPayment != null
                    ? allInvoices.Where(i => i.CanAddPayment() || i.Id == _currentPayment.InvoiceId).ToList()
                    : allInvoices.Where(i => i.CanAddPayment()).ToList();

                var invoiceItems = eligibleInvoices.Select(invoice => new InvoiceDisplayItem
                {
                    Id = invoice.Id,
                    DisplayText = $"{invoice.InvoiceNumber} - {invoice.Supplier?.Name} - {FormatCurrency(invoice.Amount)}",
                    Invoice = invoice
                }).ToList();

                InvoiceComboBox.ItemsSource = invoiceItems;
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"خطأ في تحميل الفواتير: {ex.Message}");
            }
        }
        
        private void LoadPaymentData()
        {
            if (_currentPayment == null) return;
            
            InvoiceComboBox.SelectedValue = _currentPayment.InvoiceId;
            ReceiptNumberTextBox.Text = _currentPayment.ReceiptNumber ?? string.Empty;
            PaymentDatePicker.SelectedDate = _currentPayment.PaymentDate;
            AmountTextBox.Text = _currentPayment.Amount.ToString("F0");
            SetPaymentMethodSelection(_currentPayment.Method.ToString());
            SetPaymentStatusSelection(_currentPayment.Status.ToString());
            DiscountAmountTextBox.Text = _currentPayment.DiscountAmount.ToString("F0");
            NotesTextBox.Text = _currentPayment.Notes ?? string.Empty;

            // Update refund value display
            UpdateRefundValueDisplay();

            // عرض اسم الملف فقط إذا كان هناك مرفق
            if (!string.IsNullOrEmpty(_currentPayment.AttachmentPath))
            {
                var fullPath = Path.Combine(FileHelper.GetPaymentAttachmentsDirectory(), _currentPayment.AttachmentPath);
                if (File.Exists(fullPath))
                {
                    AttachmentPathTextBox.Text = Path.GetFileName(_currentPayment.AttachmentPath);
                }
                else
                {
                    AttachmentPathTextBox.Text = "ملف غير موجود";
                }
            }
            else
            {
                AttachmentPathTextBox.Text = string.Empty;
            }

            // إعادة تعيين المسار المحدد
            _selectedAttachmentPath = null;
        }
        
        private void SetPaymentMethodSelection(string paymentMethod)
        {
            foreach (ComboBoxItem item in PaymentMethodComboBox.Items)
            {
                if (item.Tag?.ToString() == paymentMethod)
                {
                    PaymentMethodComboBox.SelectedItem = item;
                    break;
                }
            }
        }
        
        private PaymentMethod GetSelectedPaymentMethod()
        {
            if (PaymentMethodComboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
            {
                return Enum.TryParse<PaymentMethod>(item.Tag.ToString(), out var method) ? method : PaymentMethod.Cash;
            }
            return PaymentMethod.Cash;
        }

        private void SetPaymentStatusSelection(string paymentStatus)
        {
            foreach (ComboBoxItem item in PaymentStatusComboBox.Items)
            {
                if (item.Tag?.ToString() == paymentStatus)
                {
                    PaymentStatusComboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private PaymentStatus GetSelectedPaymentStatus()
        {
            if (PaymentStatusComboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
            {
                return Enum.TryParse<PaymentStatus>(item.Tag.ToString(), out var status) ? status : PaymentStatus.FullPayment;
            }
            return PaymentStatus.FullPayment;
        }
        
        private void InvoiceComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (InvoiceComboBox.SelectedItem is InvoiceDisplayItem item && item.Invoice != null)
            {
                var invoice = item.Invoice;
                InvoiceInfoTextBlock.Text = $"المبلغ الإجمالي: {FormatCurrency(invoice.Amount)}\n" +
                                          $"المبلغ المدفوع: {FormatCurrency(invoice.PaidAmount)}\n" +
                                          $"المبلغ المتبقي: {FormatCurrency(invoice.RemainingAmount)}\n" +
                                          $"تاريخ الاستحقاق: {invoice.DueDate:yyyy/MM/dd}";
                InvoiceInfoPanel.Visibility = Visibility.Visible;
                
                // For new payments, suggest remaining amount
                if (!_isEditMode && invoice.RemainingAmount > 0)
                {
                    AmountTextBox.Text = invoice.RemainingAmount.ToString("F0");
                }
            }
            else
            {
                InvoiceInfoPanel.Visibility = Visibility.Collapsed;
            }
        }
        
        private void BrowseAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختر ملف الوصل",
                    Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات الصور (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp",
                    FilterIndex = 1
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    // التحقق من صحة الملف
                    if (!File.Exists(openFileDialog.FileName))
                    {
                        _toastService.ShowError("خطأ", "الملف المحدد غير موجود");
                        return;
                    }

                    // حفظ المسار المحدد
                    _selectedAttachmentPath = openFileDialog.FileName;

                    // عرض اسم الملف فقط في الحقل
                    AttachmentPathTextBox.Text = Path.GetFileName(openFileDialog.FileName);

                    _toastService.ShowInfo("تم اختيار الملف", $"تم اختيار الملف: {Path.GetFileName(openFileDialog.FileName)}");
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في اختيار الملف: {ex.Message}");
            }
        }

        private void RemoveAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إزالة المرفق المحدد
                _selectedAttachmentPath = null;
                AttachmentPathTextBox.Text = string.Empty;

                _toastService.ShowInfo("تم الإزالة", "تم إزالة المرفق");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في إزالة المرفق: {ex.Message}");
            }
        }

        private void PaymentStatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PaymentStatusComboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
            {
                // Show/hide discount fields based on selection
                bool showDiscount = tag == "PaymentWithDiscount";
                DiscountLabel.Visibility = showDiscount ? Visibility.Visible : Visibility.Collapsed;
                DiscountAmountTextBox.Visibility = showDiscount ? Visibility.Visible : Visibility.Collapsed;

                // Show/hide refund display based on selection
                bool showRefund = tag == "PaymentWithRefund";
                RefundValueLabel.Visibility = showRefund ? Visibility.Visible : Visibility.Collapsed;
                RefundValueDisplay.Visibility = showRefund ? Visibility.Visible : Visibility.Collapsed;

                if (!showDiscount)
                {
                    DiscountAmountTextBox.Text = "0";
                }

                // Update refund value display
                UpdateRefundValueDisplay();

                // Update amount calculation if invoice is selected
                UpdateAmountCalculation();
            }
        }

        private void DiscountAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateAmountCalculation();
        }

        private void AmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateRefundValueDisplay();
        }

        private void UpdateRefundValueDisplay()
        {
            if (InvoiceComboBox.SelectedItem is InvoiceDisplayItem item && item.Invoice != null)
            {
                var status = GetSelectedPaymentStatus();
                if (status == PaymentStatus.PaymentWithRefund)
                {
                    if (decimal.TryParse(AmountTextBox.Text.Replace(",", ""), out decimal paymentAmount))
                    {
                        var refundValue = item.Invoice.RemainingAmount - paymentAmount;
                        if (refundValue > 0)
                        {
                            RefundValueText.Text = $"{refundValue:N0} د.ع";
                        }
                        else
                        {
                            RefundValueText.Text = "0 د.ع";
                        }
                    }
                    else
                    {
                        RefundValueText.Text = "0 د.ع";
                    }
                }
                else
                {
                    RefundValueText.Text = "0 د.ع";
                }
            }
            else
            {
                RefundValueText.Text = "0 د.ع";
            }
        }

        private void UpdateAmountCalculation()
        {
            if (InvoiceComboBox.SelectedItem is InvoiceDisplayItem item && item.Invoice != null)
            {
                var invoice = item.Invoice;
                var status = GetSelectedPaymentStatus();

                if (status == PaymentStatus.PaymentWithDiscount)
                {
                    // Calculate remaining amount after discount
                    if (decimal.TryParse(DiscountAmountTextBox.Text.Replace(",", ""), out decimal discount))
                    {
                        var remainingAfterDiscount = invoice.RemainingAmount - discount;
                        if (remainingAfterDiscount > 0)
                        {
                            AmountTextBox.Text = remainingAfterDiscount.ToString("F0");
                        }
                    }
                }
                else if (status == PaymentStatus.PaymentWithRefund)
                {
                    // For PaymentWithRefund, user enters payment amount and refund value is calculated
                    // No automatic calculation needed here, just update the display
                    UpdateRefundValueDisplay();
                }
                else if (status == PaymentStatus.FullPayment && !_isEditMode)
                {
                    // For full payment, suggest remaining amount
                    AmountTextBox.Text = invoice.RemainingAmount.ToString("F0");
                }
            }
        }

        private bool ValidateForm()
        {
            if (InvoiceComboBox.SelectedValue == null)
            {
                _toastService.ShowWarning("تحقق من البيانات", "يرجى اختيار الفاتورة");
                InvoiceComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ReceiptNumberTextBox.Text))
            {
                _toastService.ShowWarning("تحقق من البيانات", "يرجى إدخال رقم الوصل");
                ReceiptNumberTextBox.Focus();
                return false;
            }

            if (!PaymentDatePicker.SelectedDate.HasValue)
            {
                _toastService.ShowWarning("تحقق من البيانات", "يرجى اختيار تاريخ الدفع");
                PaymentDatePicker.Focus();
                return false;
            }

            if (!decimal.TryParse(AmountTextBox.Text.Replace(",", ""), out decimal amount) || amount <= 0)
            {
                _toastService.ShowWarning("تحقق من البيانات", "يرجى إدخال مبلغ صحيح");
                AmountTextBox.Focus();
                return false;
            }

            // Validate discount amount if payment with discount is selected
            var status = GetSelectedPaymentStatus();
            if (status == PaymentStatus.PaymentWithDiscount)
            {
                if (!decimal.TryParse(DiscountAmountTextBox.Text, out decimal discount) || discount < 0)
                {
                    _toastService.ShowWarning("تحقق من البيانات", "يرجى إدخال مبلغ خصم صحيح");
                    DiscountAmountTextBox.Focus();
                    return false;
                }

                // Check if discount is not greater than remaining amount
                if (InvoiceComboBox.SelectedItem is InvoiceDisplayItem item && item.Invoice != null)
                {
                    if (discount >= item.Invoice.RemainingAmount)
                    {
                        _toastService.ShowWarning("تحقق من البيانات", "مبلغ الخصم لا يمكن أن يكون أكبر من أو يساوي المبلغ المتبقي");
                        DiscountAmountTextBox.Focus();
                        return false;
                    }
                }
            }

            // Validate refund value if payment with refund is selected
            if (status == PaymentStatus.PaymentWithRefund)
            {
                if (InvoiceComboBox.SelectedItem is InvoiceDisplayItem item && item.Invoice != null)
                {
                    var refundValue = item.Invoice.RemainingAmount - amount;

                    if (refundValue < 0)
                    {
                        _toastService.ShowWarning("تحقق من البيانات", "المبلغ المدفوع لا يمكن أن يكون أكبر من المبلغ المتبقي");
                        AmountTextBox.Focus();
                        return false;
                    }

                    if (amount <= 0)
                    {
                        _toastService.ShowWarning("تحقق من البيانات", "يجب أن يكون المبلغ المدفوع أكبر من صفر");
                        AmountTextBox.Focus();
                        return false;
                    }
                }
            }

            return true;
        }
        
        private async Task<Payment> CreatePaymentFromFormAsync()
        {
            var discountAmount = decimal.TryParse(DiscountAmountTextBox.Text.Replace(",", ""), out decimal discount) ? discount : 0;
            var paymentAmount = decimal.Parse(AmountTextBox.Text.Replace(",", ""));

            // Calculate refund value for PaymentWithRefund status
            var refundValue = 0m;
            var status = GetSelectedPaymentStatus();
            if (status == PaymentStatus.PaymentWithRefund && InvoiceComboBox.SelectedItem is InvoiceDisplayItem item && item.Invoice != null)
            {
                refundValue = item.Invoice.RemainingAmount - paymentAmount;
                if (refundValue < 0) refundValue = 0;
            }

            // معالجة المرفق
            string? attachmentPath = null;

            if (!string.IsNullOrWhiteSpace(_selectedAttachmentPath))
            {
                // ملف جديد تم اختياره
                try
                {
                    var receiptNumber = ReceiptNumberTextBox.Text.Trim();
                    attachmentPath = await FileHelper.SavePaymentAttachmentAsync(_selectedAttachmentPath, receiptNumber);

                    // تحويل إلى مسار نسبي
                    if (!string.IsNullOrEmpty(attachmentPath))
                    {
                        var attachmentsDir = FileHelper.GetPaymentAttachmentsDirectory();
                        attachmentPath = Path.GetRelativePath(attachmentsDir, attachmentPath);
                    }
                }
                catch (Exception ex)
                {
                    _toastService.ShowWarning("تحذير", $"فشل في حفظ المرفق: {ex.Message}");
                    attachmentPath = null;
                }
            }
            else if (_isEditMode && _currentPayment != null && !string.IsNullOrEmpty(_currentPayment.AttachmentPath))
            {
                // في وضع التعديل والمرفق الحالي موجود
                attachmentPath = _currentPayment.AttachmentPath;
            }

            return new Payment
            {
                Id = _isEditMode && _currentPayment != null ? _currentPayment.Id : 0,
                InvoiceId = (int)InvoiceComboBox.SelectedValue,
                ReceiptNumber = ReceiptNumberTextBox.Text.Trim(),
                PaymentDate = PaymentDatePicker.SelectedDate!.Value,
                Amount = paymentAmount,
                DiscountAmount = discountAmount,
                RefundValue = refundValue,
                Method = GetSelectedPaymentMethod(),
                Status = status,
                Notes = string.IsNullOrWhiteSpace(NotesTextBox.Text) ? null : NotesTextBox.Text.Trim(),
                AttachmentPath = attachmentPath
            };
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                SaveButton.IsEnabled = false;

                var payment = await CreatePaymentFromFormAsync();

                if (_isEditMode)
                {
                    await _paymentService.UpdatePaymentAsync(payment);
                    _toastService.ShowSuccess("نجح العمل", "تم تحديث المدفوعة بنجاح");
                }
                else
                {
                    await _paymentService.CreatePaymentAsync(payment);
                    _toastService.ShowSuccess("نجح العمل", "تم إضافة المدفوعة بنجاح");
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"خطأ في حفظ المدفوعة: {ex.Message}");
            }
            finally
            {
                SaveButton.IsEnabled = true;
            }
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
        
        private static string FormatCurrency(decimal amount)
        {
            return $"{amount:N0} د.ع";
        }
        
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
    
    // Helper class for invoice display in ComboBox
    public class InvoiceDisplayItem
    {
        public int Id { get; set; }
        public string DisplayText { get; set; } = string.Empty;
        public Invoice? Invoice { get; set; }
    }
}
