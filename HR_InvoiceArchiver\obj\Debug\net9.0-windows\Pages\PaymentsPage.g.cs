﻿#pragma checksum "..\..\..\..\Pages\PaymentsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "EA6BD60BA8657A350C8EC5451945ABE37440FF9A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using HR_InvoiceArchiver.Converters;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// PaymentsPage
    /// </summary>
    public partial class PaymentsPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 178 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CashPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CardPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddMultiPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 327 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 348 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 378 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaymentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 681 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentCountText;
        
        #line default
        #line hidden
        
        
        #line 694 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalDisplayedAmountText;
        
        #line default
        #line hidden
        
        
        #line 707 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateText;
        
        #line default
        #line hidden
        
        
        #line 717 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 753 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid EmptyStatePanel;
        
        #line default
        #line hidden
        
        
        #line 795 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MultiPaymentOverlay;
        
        #line default
        #line hidden
        
        
        #line 812 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MultiPaymentContainer;
        
        #line default
        #line hidden
        
        
        #line 819 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PaymentDetailsOverlay;
        
        #line default
        #line hidden
        
        
        #line 836 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PaymentDetailsContainer;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/paymentspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\PaymentsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TotalPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.CashPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.CardPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.AddPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 300 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AddPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.AddMultiPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 310 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AddMultiPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddMultiPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 320 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 330 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 353 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 361 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 370 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AdvancedSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.PaymentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 18:
            this.PaymentCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.TotalDisplayedAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.LastUpdateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.LoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 22:
            this.EmptyStatePanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 23:
            
            #line 773 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 783 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.MultiPaymentOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 26:
            this.MultiPaymentContainer = ((System.Windows.Controls.Grid)(target));
            return;
            case 27:
            this.PaymentDetailsOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 28:
            this.PaymentDetailsContainer = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 13:
            
            #line 590 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewReceiptAttachment_Click);
            
            #line default
            #line hidden
            break;
            case 14:
            
            #line 610 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewPaymentDetails_Click);
            
            #line default
            #line hidden
            break;
            case 15:
            
            #line 620 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviewAttachment_Click);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 640 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditPayment_Click);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 651 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeletePayment_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

